import os
from typing import List, Optional

import httpx
from ai_agent_utils.logging import logger

from app.models.trainer_bio import Trainer<PERSON><PERSON>, TrainerBioSearchResponse

SCHEDULING_BIOS_API_BASE_URL = os.getenv("SCHEDULING_BIOS_API_BASE_URL")

async def search_trainer_bio_by_name(trainer_name: str) -> Optional[List[TrainerBio]]:
    if not SCHEDULING_BIOS_API_BASE_URL:
        logger.error(
            "SCHEDULING_BIOS_API_BASE_URL not configured.",
            facets={"action": "search_trainer_bio_by_name"},
        )
        return None

    search_url = f"{SCHEDULING_BIOS_API_BASE_URL.rstrip('/')}/v2/bios/search"

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    payload = {"query": trainer_name}

    async with httpx.AsyncClient() as client:
        try:
            logger.info(
                f"Searching trainer bio for: '{trainer_name}' at {search_url}",
                facets={"trainer_name": trainer_name, "url": search_url},
            )
            response = await client.post(search_url, headers=headers, json=payload, timeout=15.0)
            response.raise_for_status()

            data = response.json()
            validated_response = TrainerBioSearchResponse.model_validate(data)

            logger.info(
                f"Received {validated_response.count} results for trainer search: '{trainer_name}'. Returning {len(validated_response.results)} bios.",
                facets={
                    "trainer_name": trainer_name,
                    "count": validated_response.count,
                    "results_returned": len(validated_response.results),
                },
            )
            return validated_response.results
        except httpx.HTTPStatusError as e:
            logger.error(
                f"HTTP error calling Scheduling.Bios.Api for '{trainer_name}': {e.response.status_code} - {e.response.text}",
                facets={
                    "trainer_name": trainer_name,
                    "url": search_url,
                    "status_code": e.response.status_code,
                    "response_text": e.response.text,
                },
            )
            return None
        except Exception as e:
            logger.error(
                f"General error calling Scheduling.Bios.Api for '{trainer_name}': {str(e)}",
                facets={
                    "trainer_name": trainer_name,
                    "url": search_url,
                    "error_type": type(e).__name__,
                },
            )
            return None
