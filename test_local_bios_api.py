#!/usr/bin/env python3
"""
Test the local port-forwarded Scheduling Bios API
"""
import asyncio
import httpx
import json

BASE_URL = "http://localhost:8080"

async def test_base_endpoint():
    """Test the base endpoint to see what's available"""
    print(f"Testing base endpoint: {BASE_URL}")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(BASE_URL, timeout=10.0)
            print(f"Base endpoint status: {response.status_code}")
            print(f"Base endpoint response: {response.text}")
            return response.status_code == 200
        except Exception as e:
            print(f"Base endpoint error: {e}")
            return False

async def test_health_endpoint():
    """Test common health endpoints"""
    endpoints = ["/health", "/healthcheck", "/status", "/ping"]
    
    async with httpx.AsyncClient() as client:
        for endpoint in endpoints:
            try:
                url = f"{BASE_URL}{endpoint}"
                print(f"\nTrying health endpoint: {url}")
                response = await client.get(url, timeout=10.0)
                print(f"Status: {response.status_code}")
                if response.status_code == 200:
                    print(f"Response: {response.text}")
                    return True
            except Exception as e:
                print(f"Error: {e}")
    return False

async def test_bios_endpoints():
    """Test various bios-related endpoints"""
    endpoints = [
        "/bios",
        "/v1/bios",
        "/v2/bios", 
        "/api/bios",
        "/bios/search",
        "/v1/bios/search",
        "/v2/bios/search",
        "/api/bios/search"
    ]
    
    async with httpx.AsyncClient() as client:
        for endpoint in endpoints:
            try:
                url = f"{BASE_URL}{endpoint}"
                print(f"\nTrying GET {url}")
                response = await client.get(url, timeout=10.0)
                print(f"Status: {response.status_code}")
                if response.status_code in [200, 405]:  # 405 might mean POST is required
                    print(f"Response: {response.text[:200]}...")
                    
                    # If GET returns 405, try POST
                    if response.status_code == 405:
                        print(f"Trying POST {url}")
                        post_response = await client.post(
                            url, 
                            json={"query": "test", "isPublished": True, "take": 1},
                            timeout=10.0
                        )
                        print(f"POST Status: {post_response.status_code}")
                        if post_response.status_code == 200:
                            print(f"POST Response: {post_response.text[:200]}...")
                            return url, "POST"
                    elif response.status_code == 200:
                        return url, "GET"
                        
            except Exception as e:
                print(f"Error: {e}")
    return None, None

async def test_search_with_payload():
    """Test search with a proper payload"""
    search_endpoints = [
        "/bios/search",
        "/v1/bios/search", 
        "/v2/bios/search",
        "/api/bios/search"
    ]
    
    payload = {
        "query": "Sarah G.",
        "isPublished": True,
        "take": 3
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    async with httpx.AsyncClient() as client:
        for endpoint in search_endpoints:
            try:
                url = f"{BASE_URL}{endpoint}"
                print(f"\nTrying search POST {url}")
                print(f"Payload: {json.dumps(payload)}")
                
                response = await client.post(url, json=payload, headers=headers, timeout=15.0)
                print(f"Status: {response.status_code}")
                print(f"Response: {response.text}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"Parsed JSON: {json.dumps(data, indent=2)}")
                        return url, data
                    except:
                        print("Could not parse as JSON")
                        return url, response.text
                        
            except Exception as e:
                print(f"Error: {e}")
    
    return None, None

async def main():
    print("Testing local port-forwarded Scheduling Bios API")
    print("=" * 60)
    
    # Test 1: Base endpoint
    print("1. Testing base endpoint...")
    await test_base_endpoint()
    print("=" * 60)
    
    # Test 2: Health endpoints
    print("2. Testing health endpoints...")
    await test_health_endpoint()
    print("=" * 60)
    
    # Test 3: Bios endpoints
    print("3. Testing bios endpoints...")
    working_endpoint, method = await test_bios_endpoints()
    print("=" * 60)
    
    # Test 4: Search with payload
    print("4. Testing search with payload...")
    search_url, result = await test_search_with_payload()
    print("=" * 60)
    
    if search_url:
        print(f"✅ Found working search endpoint: {search_url}")
        print(f"Result: {result}")
    else:
        print("❌ No working search endpoint found")

if __name__ == "__main__":
    asyncio.run(main())
