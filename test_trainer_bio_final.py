#!/usr/bin/env python3
"""
Final test for trainer bio functionality
"""
import requests
import json
import time

def test_trainer_bio():
    """Test the trainer bio functionality"""
    print("Testing trainer bio functionality...")
    
    payload = {
        "question": "Tell me about <PERSON>.",
        "history": [],
        "scratch_pad": None
    }
    
    try:
        print("Sending request...")
        response = requests.post(
            "http://127.0.0.1:8000/v1/chats",
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            json=payload,
            timeout=120  # 2 minutes timeout
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Parsed JSON: {json.dumps(data, indent=2)}")
                return True
            except:
                print("Could not parse response as JSON")
                return False
        else:
            print(f"Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Exception: {e}")
        return False

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get("http://127.0.0.1:8000/healthcheck", timeout=10)
        print(f"Health: {response.status_code} - {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def main():
    print("=" * 60)
    print("TRAINER BIO FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Test health first
    if not test_health():
        print("❌ Health check failed, aborting")
        return
    
    print("✅ Health check passed")
    print("=" * 60)
    
    # Test trainer bio
    success = test_trainer_bio()
    
    print("=" * 60)
    if success:
        print("✅ Trainer bio test completed successfully!")
    else:
        print("❌ Trainer bio test failed")

if __name__ == "__main__":
    main()
